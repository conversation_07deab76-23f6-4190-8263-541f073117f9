info: INFO: Symbol LV_MEM_SIZE_KILOBYTES defined in multiple locations (see below). Please check if this is a correct behavior or a random name match:
    J:\ESP32-s3-qiche\managed_components\lvgl__lvgl\Kconfig:73
    J:\ESP32-s3-qiche\main\Kconfig.projbuild:2
info: INFO: Symbol BT_ENABLED defined in multiple locations (see below). Please check if this is a correct behavior or a random name match:
    J:\ESP32-s3-qiche\main\Kconfig.projbuild:7
    D:\Espressif\frameworks\esp-idf-v5.3.3\components\bt\Kconfig:3
info: INFO: Symbol BT_BLE_50_FEATURES_SUPPORTED defined in multiple locations (see below). Please check if this is a correct behavior or a random name match:
    D:\Espressif\frameworks\esp-idf-v5.3.3\components\bt\host\bluedroid\Kconfig.in:1212
    J:\ESP32-s3-qiche\main\Kconfig.projbuild:11
info: INFO: Symbol BT_BLE_42_FEATURES_SUPPORTED defined in multiple locations (see below). Please check if this is a correct behavior or a random name match:
    D:\Espressif\frameworks\esp-idf-v5.3.3\components\bt\host\bluedroid\Kconfig.in:1277
    J:\ESP32-s3-qiche\main\Kconfig.projbuild:15
CMake Warning at D:/Espressif/frameworks/esp-idf-v5.3.3/tools/cmake/gdbinit.cmake:55 (message):
  Error while generating esp_rom gdbinit
Call Stack (most recent call first):
  D:/Espressif/frameworks/esp-idf-v5.3.3/tools/cmake/project.cmake:356 (__generate_gdbinit)
  D:/Espressif/frameworks/esp-idf-v5.3.3/tools/cmake/project.cmake:923 (__project_info)
  CMakeLists.txt:26 (project)


