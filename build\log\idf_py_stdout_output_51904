-- IDF_TARGET is not set, guessed 'esp32s3' from sdkconfig 'J:/ESP32-s3-qiche/sdkconfig'
-- Found Git: D:/Espressif/tools/idf-git/2.44.0/cmd/git.exe (found version "2.44.0.windows.1")
-- ccache will be used for faster recompilation
-- The C compiler identification is GNU 13.2.0
-- The CXX compiler identification is GNU 13.2.0
-- The ASM compiler identification is GNU
-- Found assembler: D:/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: D:/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: D:/Espressif/tools/xtensa-esp-elf/esp-13.2.0_20240530/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
NOTICE: Using component placed at J:\ESP32-s3-qiche\components\espressif__esp-dsp for dependency "espressif/esp-dsp"(introduced by component "espressif/esp-sr")
NOTICE: Using component placed at J:\ESP32-s3-qiche\components\chmorgan__esp-audio-player for dependency "chmorgan/esp-audio-player", specified in J:\ESP32-s3-qiche\main\idf_component.yml
NOTICE: Using component placed at J:\ESP32-s3-qiche\components\chmorgan__esp-libhelix-mp3 for dependency "chmorgan/esp-libhelix-mp3", specified in J:\ESP32-s3-qiche\main\idf_component.yml
NOTICE: Using component placed at J:\ESP32-s3-qiche\components\espressif__esp-dsp for dependency "espressif/esp-dsp", specified in J:\ESP32-s3-qiche\components\espressif__esp-sr\idf_component.yml
NOTICE: Processing 5 dependencies:
NOTICE: [1/5] chmorgan/esp-audio-player (1.0.7) (J:\ESP32-s3-qiche\components\chmorgan__esp-audio-player)
NOTICE: [2/5] chmorgan/esp-libhelix-mp3 (1.0.3) (J:\ESP32-s3-qiche\components\chmorgan__esp-libhelix-mp3)
NOTICE: [3/5] espressif/esp-dsp (1.4.12) (J:\ESP32-s3-qiche\components\espressif__esp-dsp)
NOTICE: [4/5] lvgl/lvgl (8.3.11)
NOTICE: [5/5] idf (5.3.3)
-- Project sdkconfig file J:/ESP32-s3-qiche/sdkconfig
Loading defaults file J:/ESP32-s3-qiche/sdkconfig.defaults...
J:/ESP32-s3-qiche/sdkconfig.defaults:48 CONFIG_FLASHMODE_QIO was replaced with CONFIG_ESPTOOLPY_FLASHMODE_QIO 
J:/ESP32-s3-qiche/sdkconfig.defaults:60 CONFIG_ESP32_WIFI_RX_BA_WIN was replaced with CONFIG_ESP_WIFI_RX_BA_WIN 
J:/ESP32-s3-qiche/sdkconfig.defaults:61 CONFIG_ESP32_WIFI_RX_BA_WIN was replaced with CONFIG_ESP_WIFI_RX_BA_WIN 
warning: unknown kconfig symbol 'MODEL_IN_SPIFFS' assigned to 'y' in J:/ESP32-s3-qiche/sdkconfig.defaults
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: D:/Espressif/python_env/idf5.3_py3.11_env/Scripts/python.exe (found version "3.11.2") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- App "ESP32-S3-Touch-LCD-1.85-Test" version: 1
-- Adding linker script J:/ESP32-s3-qiche/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script J:/ESP32-s3-qiche/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script D:/Espressif/frameworks/esp-idf-v5.3.3/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt chmorgan__esp-audio-player chmorgan__esp-libhelix-mp3 cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_vfs_console esp_wifi espcoredump espressif__esp-dsp espressif__esp-sr esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lvgl__lvgl lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: D:/Espressif/frameworks/esp-idf-v5.3.3/components/app_trace D:/Espressif/frameworks/esp-idf-v5.3.3/components/app_update D:/Espressif/frameworks/esp-idf-v5.3.3/components/bootloader D:/Espressif/frameworks/esp-idf-v5.3.3/components/bootloader_support D:/Espressif/frameworks/esp-idf-v5.3.3/components/bt J:/ESP32-s3-qiche/components/chmorgan__esp-audio-player J:/ESP32-s3-qiche/components/chmorgan__esp-libhelix-mp3 D:/Espressif/frameworks/esp-idf-v5.3.3/components/cmock D:/Espressif/frameworks/esp-idf-v5.3.3/components/console D:/Espressif/frameworks/esp-idf-v5.3.3/components/cxx D:/Espressif/frameworks/esp-idf-v5.3.3/components/driver D:/Espressif/frameworks/esp-idf-v5.3.3/components/efuse D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp-tls D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_adc D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_app_format D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_bootloader_format D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_coex D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_common D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ana_cmpr D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_cam D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_dac D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_gpio D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_gptimer D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_i2c D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_i2s D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_isp D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_jpeg D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ledc D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_mcpwm D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_parlio D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_pcnt D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ppa D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_rmt D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdio D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdm D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdmmc D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdspi D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_spi D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_touch_sens D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_tsens D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_uart D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_usb_serial_jtag D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_eth D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_event D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_gdbstub D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hid D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_http_client D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_http_server D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_https_ota D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_https_server D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_lcd D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_local_ctrl D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_mm D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_netif D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_netif_stack D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_partition D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_phy D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_pm D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_psram D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_ringbuf D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_system D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_timer D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_vfs_console D:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_wifi D:/Espressif/frameworks/esp-idf-v5.3.3/components/espcoredump J:/ESP32-s3-qiche/components/espressif__esp-dsp J:/ESP32-s3-qiche/components/espressif__esp-sr D:/Espressif/frameworks/esp-idf-v5.3.3/components/esptool_py D:/Espressif/frameworks/esp-idf-v5.3.3/components/fatfs D:/Espressif/frameworks/esp-idf-v5.3.3/components/freertos D:/Espressif/frameworks/esp-idf-v5.3.3/components/hal D:/Espressif/frameworks/esp-idf-v5.3.3/components/heap D:/Espressif/frameworks/esp-idf-v5.3.3/components/http_parser D:/Espressif/frameworks/esp-idf-v5.3.3/components/idf_test D:/Espressif/frameworks/esp-idf-v5.3.3/components/ieee802154 D:/Espressif/frameworks/esp-idf-v5.3.3/components/json D:/Espressif/frameworks/esp-idf-v5.3.3/components/log J:/ESP32-s3-qiche/managed_components/lvgl__lvgl D:/Espressif/frameworks/esp-idf-v5.3.3/components/lwip J:/ESP32-s3-qiche/main D:/Espressif/frameworks/esp-idf-v5.3.3/components/mbedtls D:/Espressif/frameworks/esp-idf-v5.3.3/components/mqtt D:/Espressif/frameworks/esp-idf-v5.3.3/components/newlib D:/Espressif/frameworks/esp-idf-v5.3.3/components/nvs_flash D:/Espressif/frameworks/esp-idf-v5.3.3/components/nvs_sec_provider D:/Espressif/frameworks/esp-idf-v5.3.3/components/openthread D:/Espressif/frameworks/esp-idf-v5.3.3/components/partition_table D:/Espressif/frameworks/esp-idf-v5.3.3/components/perfmon D:/Espressif/frameworks/esp-idf-v5.3.3/components/protobuf-c D:/Espressif/frameworks/esp-idf-v5.3.3/components/protocomm D:/Espressif/frameworks/esp-idf-v5.3.3/components/pthread D:/Espressif/frameworks/esp-idf-v5.3.3/components/sdmmc D:/Espressif/frameworks/esp-idf-v5.3.3/components/soc D:/Espressif/frameworks/esp-idf-v5.3.3/components/spi_flash D:/Espressif/frameworks/esp-idf-v5.3.3/components/spiffs D:/Espressif/frameworks/esp-idf-v5.3.3/components/tcp_transport D:/Espressif/frameworks/esp-idf-v5.3.3/components/touch_element D:/Espressif/frameworks/esp-idf-v5.3.3/components/ulp D:/Espressif/frameworks/esp-idf-v5.3.3/components/unity D:/Espressif/frameworks/esp-idf-v5.3.3/components/usb D:/Espressif/frameworks/esp-idf-v5.3.3/components/vfs D:/Espressif/frameworks/esp-idf-v5.3.3/components/wear_levelling D:/Espressif/frameworks/esp-idf-v5.3.3/components/wifi_provisioning D:/Espressif/frameworks/esp-idf-v5.3.3/components/wpa_supplicant D:/Espressif/frameworks/esp-idf-v5.3.3/components/xtensa
-- Configuring done (44.9s)
-- Generating done (3.4s)
-- Build files have been written to: J:/ESP32-s3-qiche/build
