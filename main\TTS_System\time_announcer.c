#include "time_announcer.h"
#include "esp_log.h"
#include "esp_task_wdt.h"  // 【看门狗修复】添加看门狗头文件
#include "nvs_flash.h"
#include "nvs.h"
#include "UART_Driver/voice_controller.h"  // 包含Voice_Player_Is_Playing函数
#include "Audio_Driver/PCM5101.h"          // 包含Play_Music函数
#include "tts_audio_player.h"              // 【新增】TTS专用播放器
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <sys/time.h>

static const char *TAG = "TIME_ANNOUNCER";

// 中文数字转换
static const char* chinese_numbers[] = {
    "零", "一", "二", "三", "四", "五", "六", "七", "八", "九",
    "十", "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九",
    "二十", "二十一", "二十二", "二十三"
};

// 时间段描述
static const char* time_periods[] = {
    "凌晨", "早上", "上午", "中午", "下午", "傍晚", "晚上", "深夜"
};

// 精确定时器相关函数声明
static uint32_t time_announcer_calculate_ms_to_next_hour(void);
static void time_announcer_setup_precise_hourly_timer(time_announcer_handle_t announcer);
static void time_announcer_precise_hourly_callback(TimerHandle_t xTimer);
static void time_announcer_check_timer_status(time_announcer_handle_t announcer);

time_announcer_handle_t time_announcer_init(const tts_config_t *config) {
    if (config == NULL) {
        ESP_LOGE(TAG, "Invalid config parameter");
        return NULL;
    }
    
    time_announcer_t *announcer = calloc(1, sizeof(time_announcer_t));
    if (announcer == NULL) {
        ESP_LOGE(TAG, "Failed to allocate memory for time announcer");
        return NULL;
    }
    
    // 复制配置
    memcpy(&announcer->config, config, sizeof(tts_config_t));
    
    // 初始化TTS客户端
    announcer->tts_client = baidu_tts_client_init(config);
    if (announcer->tts_client == NULL) {
        ESP_LOGE(TAG, "Failed to initialize TTS client");
        free(announcer);
        return NULL;
    }

    // 【新增】初始化TTS专用播放器
    ESP_LOGI(TAG, "【播报器初始化】初始化TTS专用播放器...");
    tts_audio_config_t audio_config = tts_audio_get_default_config();
    announcer->tts_audio_player = tts_audio_player_init(&audio_config);
    if (announcer->tts_audio_player == NULL) {
        ESP_LOGE(TAG, "【播报器初始化】TTS专用播放器初始化失败");
        baidu_tts_client_destroy(announcer->tts_client);
        free(announcer);
        return NULL;
    }
    ESP_LOGI(TAG, "【播报器初始化】TTS专用播放器初始化成功");
    
    // 创建请求队列
    announcer->request_queue = xQueueCreate(TTS_QUEUE_SIZE, sizeof(time_announce_request_t));
    if (announcer->request_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create request queue");
        baidu_tts_client_destroy(announcer->tts_client);
        free(announcer);
        return NULL;
    }
    
    // 初始化状态
    announcer->state = TIME_ANNOUNCER_STATE_IDLE;
    announcer->is_running = false;
    
    ESP_LOGI(TAG, "Time announcer initialized successfully");
    return announcer;
}

void time_announcer_destroy(time_announcer_handle_t announcer) {
    if (announcer == NULL) return;
    
    // 停止服务
    time_announcer_stop(announcer);
    
    // 清理资源
    if (announcer->tts_client) {
        baidu_tts_client_destroy(announcer->tts_client);
    }

    // 【新增】清理TTS专用播放器
    if (announcer->tts_audio_player) {
        ESP_LOGI(TAG, "【播报器销毁】清理TTS专用播放器...");
        tts_audio_player_deinit((tts_audio_player_handle_t)announcer->tts_audio_player);
        announcer->tts_audio_player = NULL;
    }

    if (announcer->request_queue) {
        vQueueDelete(announcer->request_queue);
    }
    
    if (announcer->hourly_timer) {
        xTimerDelete(announcer->hourly_timer, portMAX_DELAY);
    }
    
    free(announcer);
    ESP_LOGI(TAG, "Time announcer destroyed");
}

tts_error_t time_announcer_start(time_announcer_handle_t announcer) {
    ESP_LOGI(TAG, "【播报器启动】步骤1：检查播报器状态...");

    if (announcer == NULL) {
        ESP_LOGE(TAG, "【播报器启动】步骤1失败：播报器句柄为空");
        return TTS_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "【播报器启动】播报器地址：%p", announcer);
    ESP_LOGI(TAG, "【播报器启动】当前运行状态：%d", announcer->is_running);

    if (announcer->is_running) {
        ESP_LOGW(TAG, "【播报器启动】播报器显示已在运行中，检查任务状态...");

        // 检查任务是否真的在运行
        if (announcer->task_handle != NULL) {
            eTaskState task_state = eTaskGetState(announcer->task_handle);
            ESP_LOGI(TAG, "【播报器启动】任务状态：%d", task_state);

            if (task_state == eDeleted || task_state == eInvalid) {
                ESP_LOGE(TAG, "【播报器启动】任务已删除或无效，重新创建...");
                announcer->is_running = false;
                announcer->task_handle = NULL;
            } else {
                ESP_LOGI(TAG, "【播报器启动】任务正常运行");
                return TTS_OK;
            }
        } else {
            ESP_LOGE(TAG, "【播报器启动】任务句柄为空但运行状态为true，重置状态...");
            announcer->is_running = false;
        }
    }

    ESP_LOGI(TAG, "【播报器启动】步骤1成功：播报器状态正常");
    ESP_LOGI(TAG, "【播报器启动】步骤2：创建播报任务...");
    ESP_LOGI(TAG, "【播报器启动】任务参数 - 栈大小:%d, 优先级:%d", TTS_TASK_STACK_SIZE, TTS_TASK_PRIORITY);

    // 检查内存状态
    size_t free_heap = esp_get_free_heap_size();
    size_t min_free_heap = esp_get_minimum_free_heap_size();
    ESP_LOGI(TAG, "【播报器启动】内存状态 - 可用:%u字节, 历史最小:%u字节", (unsigned)free_heap, (unsigned)min_free_heap);

    if (free_heap < TTS_TASK_STACK_SIZE + 4096) {
        ESP_LOGE(TAG, "【播报器启动】步骤2失败：内存不足，需要:%d字节，可用:%u字节",
                 TTS_TASK_STACK_SIZE + 4096, (unsigned)free_heap);
        return TTS_ERR_NO_MEMORY;
    }

    // 🔧 修复时序问题：在创建任务之前设置运行状态
    ESP_LOGI(TAG, "【播报器启动】步骤2a：设置运行状态...");
    announcer->is_running = true;
    ESP_LOGI(TAG, "【播报器启动】步骤2a成功：运行状态已设置为true");

    // 【根本修复】使用PSRAM创建任务，避免内部RAM碎片化
    ESP_LOGI(TAG, "【播报器启动】尝试使用PSRAM创建任务，栈大小:%d字节", TTS_TASK_STACK_SIZE);

    BaseType_t ret = xTaskCreatePinnedToCore(
        time_announcer_task,
        "time_announcer",
        TTS_TASK_STACK_SIZE,
        announcer,
        TTS_TASK_PRIORITY,
        &announcer->task_handle,
        1  // 【WiFi修复】固定到核心1，避免与LVGL竞争
    );

    // 如果失败，尝试减小栈大小
    if (ret != pdPASS) {
        ESP_LOGW(TAG, "【播报器启动】标准栈大小创建失败，尝试减小栈大小");
        ret = xTaskCreatePinnedToCore(
            time_announcer_task,
            "time_announcer",
            TTS_TASK_STACK_SIZE / 2,  // 减半到3.5KB
            announcer,
            TTS_TASK_PRIORITY,
            &announcer->task_handle,
            1  // 【WiFi修复】固定到核心1
        );
    }

    ESP_LOGI(TAG, "【播报器启动】xTaskCreate返回值：%d (pdPASS=%d)", ret, pdPASS);
    ESP_LOGI(TAG, "【播报器启动】任务句柄：%p", announcer->task_handle);

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "【播报器启动】步骤2失败：创建播报任务失败");
        ESP_LOGE(TAG, "【播报器启动】失败详情 - 返回值:%d, 栈大小:%d, 优先级:%d",
                 ret, TTS_TASK_STACK_SIZE, TTS_TASK_PRIORITY);
        announcer->is_running = false;  // 恢复状态
        return TTS_ERR_NO_MEMORY;
    }

    ESP_LOGI(TAG, "【播报器启动】步骤2成功：播报任务创建完成，任务句柄:%p", announcer->task_handle);

    // 等待任务启动
    vTaskDelay(pdMS_TO_TICKS(100));
    ESP_LOGI(TAG, "【播报器启动】等待任务启动完成...");
    
    ESP_LOGI(TAG, "【播报器启动】步骤3：检查整点播报设置...");
    ESP_LOGI(TAG, "【播报器启动】整点播报启用：%d", announcer->config.hourly_announce_enabled);

    // 创建精确整点播报定时器（如果启用）
    if (announcer->config.hourly_announce_enabled) {
        ESP_LOGI(TAG, "【播报器启动】步骤3a：创建精确整点播报定时器...");

        // 使用精确定时器方案，直接设置到下一个整点
        time_announcer_setup_precise_hourly_timer(announcer);

        ESP_LOGI(TAG, "【播报器启动】步骤3b成功：精确整点播报定时器启动完成");
    } else {
        ESP_LOGI(TAG, "【播报器启动】步骤3跳过：整点播报未启用");
    }

    ESP_LOGI(TAG, "【播报器启动】步骤4：确认运行状态...");
    ESP_LOGI(TAG, "【播报器启动】当前运行状态：%d", announcer->is_running);
    ESP_LOGI(TAG, "【播报器启动】全部完成：播报器启动成功！");

    return TTS_OK;
}

tts_error_t time_announcer_stop(time_announcer_handle_t announcer) {
    if (announcer == NULL) {
        return TTS_ERR_INVALID_PARAM;
    }
    
    if (!announcer->is_running) {
        return TTS_OK;
    }
    
    announcer->is_running = false;
    
    // 停止定时器
    if (announcer->hourly_timer) {
        xTimerStop(announcer->hourly_timer, portMAX_DELAY);
        xTimerDelete(announcer->hourly_timer, portMAX_DELAY);
        announcer->hourly_timer = NULL;
    }
    
    // 停止任务
    if (announcer->task_handle) {
        vTaskDelete(announcer->task_handle);
        announcer->task_handle = NULL;
    }
    
    ESP_LOGI(TAG, "Time announcer stopped");
    return TTS_OK;
}

tts_error_t time_announcer_request(time_announcer_handle_t announcer,
                                   tts_request_type_t type,
                                   tts_priority_t priority,
                                   bool force_announce) {
    ESP_LOGI(TAG, "【请求发送】步骤1：检查播报器参数...");

    if (announcer == NULL) {
        ESP_LOGE(TAG, "【请求发送】步骤1失败：播报器句柄为空");
        return TTS_ERR_INVALID_PARAM;
    }

    if (!announcer->is_running) {
        ESP_LOGE(TAG, "【请求发送】步骤1失败：播报器未运行");
        return TTS_ERR_INVALID_PARAM;
    }

    if (!announcer->request_queue) {
        ESP_LOGE(TAG, "【请求发送】步骤1失败：请求队列为空");
        return TTS_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "【请求发送】步骤1成功：播报器状态正常");
    ESP_LOGI(TAG, "【请求发送】步骤2：获取当前时间...");

    // 获取当前时间
    time_t now;
    time(&now);
    struct tm time_info;
    localtime_r(&now, &time_info);

    ESP_LOGI(TAG, "【请求发送】步骤2成功：时间 %02d:%02d:%02d",
             time_info.tm_hour, time_info.tm_min, time_info.tm_sec);

    ESP_LOGI(TAG, "【请求发送】步骤3：创建播报请求 - 类型:%d, 优先级:%d, 强制:%d",
             type, priority, force_announce);

    // 创建播报请求
    time_announce_request_t request = {
        .type = type,
        .priority = priority,
        .force_announce = force_announce,
        .time_info = time_info
    };

    ESP_LOGI(TAG, "【请求发送】步骤4：发送请求到队列（队列地址:%p）...", announcer->request_queue);

    // 检查队列状态
    UBaseType_t queue_spaces = uxQueueSpacesAvailable(announcer->request_queue);
    UBaseType_t queue_waiting = uxQueueMessagesWaiting(announcer->request_queue);
    ESP_LOGI(TAG, "【请求发送】队列状态 - 可用空间:%d, 等待消息:%d", queue_spaces, queue_waiting);

    // 发送到队列
    BaseType_t ret = xQueueSend(announcer->request_queue, &request, pdMS_TO_TICKS(1000));
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "【请求发送】步骤4失败：队列发送失败，返回值:%d", ret);
        return TTS_ERR_TIMEOUT;
    }

    ESP_LOGI(TAG, "【请求发送】步骤4成功：请求已发送到队列");

    // 【防重复优化】对于整点播报，立即更新last_announce_time防止重复触发
    if (type == TTS_REQUEST_TYPE_HOURLY && !force_announce) {
        announcer->last_announce_time = time_info;
        ESP_LOGI(TAG, "【防重复】整点播报时间戳已更新: %02d:%02d", time_info.tm_hour, time_info.tm_min);
    }

    // 再次检查队列状态
    queue_spaces = uxQueueSpacesAvailable(announcer->request_queue);
    queue_waiting = uxQueueMessagesWaiting(announcer->request_queue);
    ESP_LOGI(TAG, "【请求发送】发送后队列状态 - 可用空间:%d, 等待消息:%d", queue_spaces, queue_waiting);
    ESP_LOGI(TAG, "Time announce request sent: type=%d, priority=%d", type, priority);
    return TTS_OK;
}

tts_error_t time_announcer_generate_time_text(const struct tm *time_info,
                                              tts_request_type_t type,
                                              char *text_buffer,
                                              size_t buffer_size) {
    if (time_info == NULL || text_buffer == NULL) {
        return TTS_ERR_INVALID_PARAM;
    }
    
    int hour = time_info->tm_hour;
    int minute = time_info->tm_min;
    
    // 确定时间段
    const char *period;
    if (hour >= 0 && hour < 6) {
        period = time_periods[0]; // 凌晨
    } else if (hour >= 6 && hour < 9) {
        period = time_periods[1]; // 早上
    } else if (hour >= 9 && hour < 12) {
        period = time_periods[2]; // 上午
    } else if (hour >= 12 && hour < 14) {
        period = time_periods[3]; // 中午
    } else if (hour >= 14 && hour < 17) {
        period = time_periods[4]; // 下午
    } else if (hour == 17 && minute < 30) {
        period = time_periods[4]; // 下午（修复17:00-17:29的时间段空隙）
    } else if ((hour == 17 && minute >= 30) || (hour == 18) || (hour == 19 && minute < 30)) {
        period = "下班"; // 下班
    } else if (hour == 19 && minute >= 30) {
        period = time_periods[5]; // 傍晚
    } else if (hour >= 20 && hour < 23) {
        period = time_periods[6]; // 晚上
    } else {
        period = time_periods[7]; // 深夜
    }
    
    // 转换小时（12小时制）
    int display_hour = hour;
    if (hour == 0) {
        display_hour = 12;
    } else if (hour > 12) {
        display_hour = hour - 12;
    }
    
    // 生成时间文本
    if (type == TTS_REQUEST_TYPE_HOURLY && minute == 0) {
        // 整点播报
        snprintf(text_buffer, buffer_size, "现在是%s%s点整",
                 period, chinese_numbers[display_hour]);
    } else {
        // 普通时间播报
        if (minute == 0) {
            snprintf(text_buffer, buffer_size, "现在是%s%s点整",
                     period, chinese_numbers[display_hour]);
        } else if (minute < 10) {
            // 个位数分钟添加"零"前缀
            snprintf(text_buffer, buffer_size, "现在是%s%s点零%s分",
                     period, chinese_numbers[display_hour], chinese_numbers[minute]);
        } else if (minute < 24) {
            snprintf(text_buffer, buffer_size, "现在是%s%s点%s分",
                     period, chinese_numbers[display_hour], chinese_numbers[minute]);
        } else {
            snprintf(text_buffer, buffer_size, "现在是%s%s点%d分",
                     period, chinese_numbers[display_hour], minute);
        }
    }
    
    return TTS_OK;
}

bool time_announcer_is_in_announce_time(time_announcer_handle_t announcer,
                                        const struct tm *time_info) {
    if (announcer == NULL || time_info == NULL) {
        return false;
    }
    
    int hour = time_info->tm_hour;
    int start_hour = announcer->config.announce_start_hour;
    int end_hour = announcer->config.announce_end_hour;
    
    // 处理跨天的情况
    if (start_hour <= end_hour) {
        return (hour >= start_hour && hour <= end_hour);
    } else {
        return (hour >= start_hour || hour <= end_hour);
    }
}

bool time_announcer_should_hourly_announce(time_announcer_handle_t announcer,
                                           const struct tm *time_info) {
    if (announcer == NULL || time_info == NULL) {
        ESP_LOGE(TAG, "【整点检查】参数为空");
        return false;
    }

    ESP_LOGI(TAG, "【整点检查】开始检查 - 时间: %02d:%02d:%02d",
             time_info->tm_hour, time_info->tm_min, time_info->tm_sec);

    // 检查是否启用整点播报
    if (!announcer->config.hourly_announce_enabled) {
        ESP_LOGI(TAG, "【整点检查】整点播报未启用");
        return false;
    }

    // 【精确定时器模式】优化整点判断逻辑 - 收紧时间窗口
    // 只允许在59分30秒-00分30秒的1分钟窗口内播报，防止提前触发
    bool in_valid_window = false;
    if (time_info->tm_min == 59 && time_info->tm_sec >= 30) {
        in_valid_window = true;  // 59分30秒-59分59秒
    } else if (time_info->tm_min == 0 && time_info->tm_sec <= 30) {
        in_valid_window = true;  // 00分00秒-00分30秒
    }

    if (!in_valid_window) {
        ESP_LOGI(TAG, "【整点检查】不在整点时间窗口内 - 时间: %02d:%02d", time_info->tm_min, time_info->tm_sec);
        return false;
    }

    // 检查是否在播报时间范围内
    if (!time_announcer_is_in_announce_time(announcer, time_info)) {
        ESP_LOGI(TAG, "【整点检查】不在播报时间范围内 - 小时: %d", time_info->tm_hour);
        return false;
    }

    // 【精确定时器优化】检查是否已经播报过这个小时
    // 加强防重复播报：检查同一小时内是否已播报，特别是整点时刻
    if (announcer->last_announce_time.tm_hour == time_info->tm_hour &&
        announcer->last_announce_time.tm_mday == time_info->tm_mday &&
        announcer->last_announce_time.tm_year == time_info->tm_year) {

        // 【加强检查】如果是整点播报，还要检查分钟是否相近（防止同一分钟内重复）
        if (time_info->tm_min == 0 && announcer->last_announce_time.tm_min == 0) {
            ESP_LOGI(TAG, "【整点检查】本小时整点已播报过 - 上次播报: %04d-%02d-%02d %02d:%02d",
                     announcer->last_announce_time.tm_year + 1900,
                     announcer->last_announce_time.tm_mon + 1,
                     announcer->last_announce_time.tm_mday,
                     announcer->last_announce_time.tm_hour,
                     announcer->last_announce_time.tm_min);
            return false;
        }

        ESP_LOGI(TAG, "【整点检查】本小时已播报过 - 上次播报: %04d-%02d-%02d %02d:%02d",
                 announcer->last_announce_time.tm_year + 1900,
                 announcer->last_announce_time.tm_mon + 1,
                 announcer->last_announce_time.tm_mday,
                 announcer->last_announce_time.tm_hour,
                 announcer->last_announce_time.tm_min);
        return false;
    }

    ESP_LOGI(TAG, "【整点检查】满足播报条件 - 将播报 %02d:00", time_info->tm_hour);
    return true;
}

/**
 * @brief 计算到下一个整点的精确毫秒数
 * @return 到下一个整点的毫秒数（提前5秒触发）
 */
static uint32_t time_announcer_calculate_ms_to_next_hour(void) {
    time_t now;
    time(&now);
    struct tm time_info;
    localtime_r(&now, &time_info);

    ESP_LOGI(TAG, "【精确定时器】当前时间: %02d:%02d:%02d",
             time_info.tm_hour, time_info.tm_min, time_info.tm_sec);

    // 【修复】计算到下一个整点还有多少秒
    // 当前时间：HH:MM:SS，目标时间：(HH+1):00:00
    int minutes_left = 59 - time_info.tm_min;
    int seconds_left = 60 - time_info.tm_sec;

    // 【边界修复】处理秒数为0的情况
    if (time_info.tm_sec == 0) {
        seconds_left = 60;
    }

    uint32_t total_seconds = minutes_left * 60 + seconds_left;

    // 【边界修复】如果已经是整点（MM:00），则设置到下一个小时
    if (time_info.tm_min == 0 && time_info.tm_sec == 0) {
        total_seconds = 3600;  // 1小时后
    }

    // 【优化触发时机】提前30秒触发，确保在59分30秒左右触发
    // 这样可以进入时间窗口，然后通过动态延迟精确等待到整点
    if (total_seconds > 30) {
        total_seconds -= 30;
    } else {
        // 如果已经接近整点，设置到下一个小时
        total_seconds = 3600 - 30;
    }

    uint32_t total_ms = total_seconds * 1000;

    // 计算目标时间
    int target_hour = (time_info.tm_hour + 1) % 24;
    ESP_LOGI(TAG, "【精确定时器】设置到 %02d:00 的定时器，延迟 %lu 秒",
             target_hour, (unsigned long)(total_seconds));

    return total_ms;
}

/**
 * @brief 设置精确整点定时器
 */
static void time_announcer_setup_precise_hourly_timer(time_announcer_handle_t announcer) {
    if (announcer == NULL) {
        ESP_LOGE(TAG, "【精确定时器】播报器句柄为空");
        return;
    }

    // 删除旧定时器
    if (announcer->hourly_timer != NULL) {
        xTimerDelete(announcer->hourly_timer, pdMS_TO_TICKS(100));
        announcer->hourly_timer = NULL;
    }

    // 计算到下一个整点的精确时间
    uint32_t ms_to_next_hour = time_announcer_calculate_ms_to_next_hour();

    // 创建一次性精确定时器
    announcer->hourly_timer = xTimerCreate(
        "precise_hourly",
        pdMS_TO_TICKS(ms_to_next_hour),
        pdFALSE,  // 一次性定时器
        announcer,
        time_announcer_precise_hourly_callback
    );

    if (announcer->hourly_timer == NULL) {
        ESP_LOGE(TAG, "【精确定时器】创建定时器失败");
        return;
    }

    BaseType_t timer_ret = xTimerStart(announcer->hourly_timer, pdMS_TO_TICKS(100));
    if (timer_ret != pdPASS) {
        ESP_LOGE(TAG, "【精确定时器】启动定时器失败: %d", timer_ret);
        xTimerDelete(announcer->hourly_timer, pdMS_TO_TICKS(100));
        announcer->hourly_timer = NULL;
    } else {
        ESP_LOGI(TAG, "【精确定时器】精确整点定时器设置成功");
    }
}

/**
 * @brief 精确整点定时器回调函数
 * 【优化】避免在定时器回调中阻塞，使用任务通知机制
 */
static void time_announcer_precise_hourly_callback(TimerHandle_t xTimer) {
    time_announcer_handle_t announcer = (time_announcer_handle_t)pvTimerGetTimerID(xTimer);
    if (announcer == NULL) {
        ESP_LOGE(TAG, "【精确整点】定时器回调：播报器句柄为空");
        return;
    }

    ESP_LOGI(TAG, "【精确整点】定时器触发，通知任务处理整点播报...");

    // 【优化】使用任务通知替代阻塞延迟，避免阻塞定时器服务任务
    if (announcer->task_handle != NULL) {
        // 发送特殊通知值表示精确整点触发
        BaseType_t notify_result = xTaskNotify(announcer->task_handle,
                                               0x12345678,  // 特殊值标识精确整点
                                               eSetValueWithOverwrite);
        if (notify_result == pdPASS) {
            ESP_LOGI(TAG, "【精确整点】任务通知发送成功");
        } else {
            ESP_LOGE(TAG, "【精确整点】任务通知发送失败");
        }
    } else {
        ESP_LOGE(TAG, "【精确整点】任务句柄为空，无法发送通知");
    }

    // 重新设置下一个整点的精确定时器
    ESP_LOGI(TAG, "【精确整点】重新设置下一个整点定时器");
    time_announcer_setup_precise_hourly_timer(announcer);
}

// 保留原有回调函数作为备用（兼容性）
void time_announcer_hourly_timer_callback(TimerHandle_t xTimer) {
    // 重定向到精确定时器回调
    time_announcer_precise_hourly_callback(xTimer);
}

void time_announcer_task(void *pvParameters) {
    time_announcer_handle_t announcer = (time_announcer_handle_t)pvParameters;
    time_announce_request_t request;

    ESP_LOGI(TAG, "【任务启动】播报任务开始 - 播报器地址:%p", announcer);

    // 添加任务启动延迟，确保系统稳定
    vTaskDelay(pdMS_TO_TICKS(100));

    if (!announcer) {
        ESP_LOGE(TAG, "【任务启动】失败：播报器句柄为空！");
        vTaskDelete(NULL);
        return;
    }

    ESP_LOGI(TAG, "【任务启动】播报器状态 - 运行:%d, 队列:%p",
             announcer->is_running, announcer->request_queue);

    if (!announcer->request_queue) {
        ESP_LOGE(TAG, "【任务启动】失败：请求队列为空！");
        announcer->is_running = false;
        vTaskDelete(NULL);
        return;
    }

    ESP_LOGI(TAG, "【任务启动】成功：播报任务正常运行，开始监听请求...");

    // 【看门狗修复】尝试注册当前任务到看门狗
    esp_err_t wdt_add_result = esp_task_wdt_add(NULL);  // NULL表示当前任务
    bool is_wdt_registered = false;
    if (wdt_add_result == ESP_OK) {
        ESP_LOGI(TAG, "【看门狗】TTS任务已注册到看门狗系统");
        is_wdt_registered = true;
    } else if (wdt_add_result == ESP_ERR_INVALID_STATE) {
        ESP_LOGD(TAG, "【看门狗】TTS任务已经注册过看门狗，跳过");
        is_wdt_registered = true;  // 已注册状态
    } else {
        ESP_LOGW(TAG, "【看门狗】TTS任务注册看门狗失败: %s", esp_err_to_name(wdt_add_result));
        is_wdt_registered = false;
    }

    // 确保任务状态正确
    announcer->state = TIME_ANNOUNCER_STATE_IDLE;

    // 添加任务运行状态指示
    int loop_count = 0;

    while (announcer->is_running) {
        // 每10秒输出一次任务运行状态
        if (loop_count % 100 == 0) {
            ESP_LOGI(TAG, "【任务状态】播报任务正在运行，循环计数:%d", loop_count);
        }

        // 【新增】每5分钟检查一次定时器状态（300次循环 = 5分钟）
        if (loop_count % 300 == 0) {
            time_announcer_check_timer_status(announcer);
        }

        // 等待播报请求
        ESP_LOGD(TAG, "【任务循环】等待播报请求...");

        // 【看门狗修复】只有已注册的任务才重置看门狗
        if (is_wdt_registered) {
            esp_err_t wdt_reset_result = esp_task_wdt_reset();
            if (wdt_reset_result != ESP_OK && loop_count % 1000 == 0) {
                // 【减少日志频率】只在每1000次循环失败时记录一次
                ESP_LOGW(TAG, "【看门狗】重置失败: %s", esp_err_to_name(wdt_reset_result));
                is_wdt_registered = false;  // 标记为未注册，避免后续重试
            }
        }

        // 【精确整点优化】检查任务通知（精确整点触发）
        uint32_t notification_value = 0;
        BaseType_t notify_result = xTaskNotifyWait(0, 0xFFFFFFFF, &notification_value, 0);

        if (notify_result == pdPASS && notification_value == 0x12345678) {
            ESP_LOGI(TAG, "【精确整点】收到精确整点通知，动态计算等待时间到达整点...");

            // 【动态延迟计算】获取当前时间，计算到整点00秒的精确等待时间
            time_t now_temp;
            time(&now_temp);
            struct tm time_info_temp;
            localtime_r(&now_temp, &time_info_temp);

            int wait_seconds = 0;
            if (time_info_temp.tm_min == 59) {
                // 如果是59分，等待到下一个整点00秒
                wait_seconds = 60 - time_info_temp.tm_sec;
            } else if (time_info_temp.tm_min == 0) {
                // 如果已经是00分，根据秒数决定等待时间
                if (time_info_temp.tm_sec <= 30) {
                    wait_seconds = 0; // 已经在整点窗口内，不需要等待
                } else {
                    wait_seconds = 60 - time_info_temp.tm_sec; // 等待到下一个整点
                }
            } else {
                // 其他情况，计算到下一个整点的时间
                wait_seconds = (60 - time_info_temp.tm_min) * 60 - time_info_temp.tm_sec;
            }

            ESP_LOGI(TAG, "【精确整点】当前时间 %02d:%02d:%02d，等待 %d 秒到达整点",
                     time_info_temp.tm_hour, time_info_temp.tm_min, time_info_temp.tm_sec, wait_seconds);

            if (wait_seconds > 0 && wait_seconds <= 60) {
                vTaskDelay(pdMS_TO_TICKS(wait_seconds * 1000));
            }

            // 获取当前时间（应该是整点）
            time_t now;
            time(&now);
            struct tm time_info;
            localtime_r(&now, &time_info);

            ESP_LOGI(TAG, "【精确整点】当前时间: %02d:%02d:%02d",
                     time_info.tm_hour, time_info.tm_min, time_info.tm_sec);

            // 【防重复优化】检查播放状态，避免重复触发
            if (announcer->state == TIME_ANNOUNCER_STATE_PLAYING ||
                announcer->state == TIME_ANNOUNCER_STATE_GENERATING) {
                ESP_LOGI(TAG, "【精确整点】播报器忙碌（状态:%d），跳过本次整点播报", announcer->state);
                continue;
            }

            // 检查是否需要整点播报
            if (time_announcer_should_hourly_announce(announcer, &time_info)) {
                ESP_LOGI(TAG, "【精确整点】触发整点播报: %02d:00", time_info.tm_hour);

                // 在整点播报时检查WiFi网络状态
                extern bool WIFI_Connected;
                extern void wifi_ui_set_reconnecting_status(void);

                if (!WIFI_Connected) {
                    ESP_LOGW(TAG, "【精确整点】检测到WiFi断开，触发重连检测");
                    wifi_ui_set_reconnecting_status();
                    ESP_LOGI(TAG, "【精确整点】将在后台触发WiFi重连检测");
                }

                time_announcer_request(announcer, TTS_REQUEST_TYPE_HOURLY, TTS_PRIORITY_NORMAL, false);
            } else {
                ESP_LOGI(TAG, "【精确整点】当前时间不需要播报 - %02d:%02d",
                         time_info.tm_hour, time_info.tm_min);
            }

            // 继续下一次循环，不处理队列
            continue;
        }

        // 使用try-catch风格的错误处理
        BaseType_t queue_result = xQueueReceive(announcer->request_queue, &request, pdMS_TO_TICKS(1000));

        if (queue_result == pdPASS) {
            ESP_LOGI(TAG, "【任务处理】收到播报请求 - 类型:%d, 优先级:%d, 强制:%d",
                     request.type, request.priority, request.force_announce);

            // 处理请求 - 添加异常保护
            ESP_LOGI(TAG, "【任务处理】开始处理播报请求...");

            // 检查内存状态
            size_t free_heap = esp_get_free_heap_size();
            if (free_heap < 10240) {  // 少于10KB内存时跳过处理
                ESP_LOGE(TAG, "【任务处理】内存不足，跳过处理：%u字节", (unsigned)free_heap);
                continue;
            }

            tts_error_t err = time_announcer_process_request(announcer, &request);
            if (err != TTS_OK) {
                ESP_LOGE(TAG, "【任务处理】失败：播报请求处理失败，错误码:%d", err);
                announcer->state = TIME_ANNOUNCER_STATE_ERROR;

                // 错误恢复
                vTaskDelay(pdMS_TO_TICKS(1000));  // 等待1秒后继续
                announcer->state = TIME_ANNOUNCER_STATE_IDLE;
            } else {
                ESP_LOGI(TAG, "【任务处理】成功：播报请求处理完成");
            }
        } else {
            ESP_LOGD(TAG, "【任务循环】无请求（超时）");
        }

        // 短暂延迟，避免CPU占用过高
        vTaskDelay(pdMS_TO_TICKS(100));
        loop_count++;

        // 防止计数器溢出
        if (loop_count > 1000000) {
            loop_count = 0;
        }
    }

    ESP_LOGI(TAG, "Time announcer task ended");
    vTaskDelete(NULL);
}

tts_error_t time_announcer_process_request(time_announcer_handle_t announcer,
                                          const time_announce_request_t *request) {
    ESP_LOGI(TAG, "【请求处理】步骤1：检查参数...");

    if (announcer == NULL || request == NULL) {
        ESP_LOGE(TAG, "【请求处理】步骤1失败：参数为空");
        return TTS_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "【请求处理】步骤1成功：参数检查通过");
    ESP_LOGI(TAG, "【请求处理】步骤2：检查语音播放器状态...");

    // 检查是否有其他语音正在播放（除非强制播报）
    bool voice_playing = Voice_Player_Is_Playing();
    ESP_LOGI(TAG, "【请求处理】语音播放器状态：%s", voice_playing ? "正在播放" : "空闲");

    if (!request->force_announce && voice_playing) {
        ESP_LOGI(TAG, "【请求处理】步骤2等待：语音播放器忙碌，等待播放完成...");

        // 等待串口语音播放完成，最多等待30秒
        uint32_t wait_start = xTaskGetTickCount() * portTICK_PERIOD_MS;
        const uint32_t MAX_WAIT_TIME = 30000; // 30秒超时

        while (Voice_Player_Is_Playing() &&
               (xTaskGetTickCount() * portTICK_PERIOD_MS - wait_start) < MAX_WAIT_TIME) {
            vTaskDelay(pdMS_TO_TICKS(200)); // 每200ms检查一次

            // 每5秒输出一次等待状态
            uint32_t elapsed = xTaskGetTickCount() * portTICK_PERIOD_MS - wait_start;
            if (elapsed % 5000 < 200) {
                ESP_LOGI(TAG, "【请求处理】等待串口语音完成中...已等待%lu毫秒", (unsigned long)elapsed);
            }
        }

        // 检查等待结果
        if (Voice_Player_Is_Playing()) {
            ESP_LOGW(TAG, "【请求处理】等待超时，串口语音仍在播放，跳过TTS播报");
            return TTS_OK;
        } else {
            ESP_LOGI(TAG, "【请求处理】串口语音播放完成，继续TTS播报");
        }
    }

    ESP_LOGI(TAG, "【请求处理】步骤2成功：语音播放器可用");
    ESP_LOGI(TAG, "【请求处理】步骤3：检查播报时间限制...");

    // 检查播报时间限制（除非强制播报）
    if (!request->force_announce &&
        !time_announcer_is_in_announce_time(announcer, &request->time_info)) {
        ESP_LOGW(TAG, "【请求处理】步骤3跳过：不在播报时间范围内");
        return TTS_OK;
    }

    ESP_LOGI(TAG, "【请求处理】步骤3成功：时间检查通过（强制模式:%d）", request->force_announce);
    ESP_LOGI(TAG, "【请求处理】步骤4：开始生成时间文本...");

    announcer->state = TIME_ANNOUNCER_STATE_GENERATING;

    // 生成时间文本
    char time_text[TTS_MAX_TEXT_LENGTH];
    tts_error_t err = time_announcer_generate_time_text(&request->time_info,
                                                        request->type,
                                                        time_text,
                                                        sizeof(time_text));
    if (err != TTS_OK) {
        ESP_LOGE(TAG, "【请求处理】步骤4失败：生成时间文本失败，错误码:%d", err);
        return err;
    }

    ESP_LOGI(TAG, "【请求处理】步骤4成功：生成时间文本 - \"%s\"", time_text);

    ESP_LOGI(TAG, "【请求处理】步骤5：开始百度TTS语音合成...");
    ESP_LOGI(TAG, "【请求处理】TTS客户端状态：%p", announcer->tts_client);

    // 【性能优化】在TTS合成前进行内存整理
    size_t free_before = esp_get_free_heap_size();
    size_t internal_before = heap_caps_get_free_size(MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
    ESP_LOGI(TAG, "【性能优化】TTS前内存状态 - 总可用:%zu, 内部RAM:%zu", free_before, internal_before);

    if (internal_before < 12000) {
        ESP_LOGW(TAG, "【性能优化】内部RAM不足，进行内存整理...");
        vTaskDelay(pdMS_TO_TICKS(10));  // 短暂延迟，让系统进行垃圾回收
        size_t internal_after = heap_caps_get_free_size(MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
        ESP_LOGI(TAG, "【性能优化】内存整理后内部RAM: %zu字节", internal_after);
    }

    // 步骤5a：网络连通性测试
    ESP_LOGI(TAG, "【请求处理】步骤5a：测试网络连通性...");
    tts_error_t network_err = baidu_tts_test_network_connectivity(announcer->tts_client);
    if (network_err != TTS_OK) {
        ESP_LOGE(TAG, "【请求处理】步骤5a失败：网络连通性测试失败，错误码:%d", network_err);
        return network_err;
    }
    ESP_LOGI(TAG, "【请求处理】步骤5a成功：网络连通性正常");

    // 步骤5b：调用百度TTS进行语音合成
    ESP_LOGI(TAG, "【请求处理】步骤5b：开始语音合成...");
    uint8_t *audio_data = NULL;
    size_t audio_size = 0;

    err = baidu_tts_synthesize(announcer->tts_client, time_text, &audio_data, &audio_size);
    if (err != TTS_OK) {
        ESP_LOGE(TAG, "【请求处理】步骤5失败：百度TTS语音合成失败，错误码:%d", err);
        const char *error_msg = baidu_tts_get_error_message(announcer->tts_client);
        ESP_LOGE(TAG, "【请求处理】TTS错误详情：%s", error_msg ? error_msg : "未知错误");
        strncpy(announcer->error_message, error_msg, sizeof(announcer->error_message) - 1);
        return err;
    }

    ESP_LOGI(TAG, "【请求处理】步骤5成功：语音合成完成，音频大小:%u字节", (unsigned)audio_size);

    ESP_LOGI(TAG, "【请求处理】步骤6：开始播放音频...");

    // 播放音频
    announcer->state = TIME_ANNOUNCER_STATE_PLAYING;
    err = time_announcer_play_audio(announcer, audio_data, audio_size);

    ESP_LOGI(TAG, "【请求处理】步骤7：释放音频数据...");
    // 释放音频数据
    baidu_tts_free_audio_data(audio_data);

    if (err == TTS_OK) {
        // 更新最后播报时间
        announcer->last_announce_time = request->time_info;
        announcer->state = TIME_ANNOUNCER_STATE_IDLE;
        ESP_LOGI(TAG, "【请求处理】步骤6成功：音频播放完成");
        ESP_LOGI(TAG, "【请求处理】全部完成：时间播报成功！");
    } else {
        ESP_LOGE(TAG, "【请求处理】步骤6失败：音频播放失败，错误码:%d", err);
        announcer->state = TIME_ANNOUNCER_STATE_ERROR;
    }

    return err;
}

tts_error_t time_announcer_play_audio(time_announcer_handle_t announcer,
                                      const uint8_t *audio_data,
                                      size_t audio_size) {
    ESP_LOGI(TAG, "【音频播放】步骤1：检查播放参数...");

    if (announcer == NULL || audio_data == NULL || audio_size == 0) {
        ESP_LOGE(TAG, "【音频播放】步骤1失败：参数无效 - 播报器:%p, 音频数据:%p, 大小:%u",
                 announcer, audio_data, (unsigned)audio_size);
        return TTS_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "【音频播放】步骤1成功：参数检查通过，音频大小:%u字节", (unsigned)audio_size);
    ESP_LOGI(TAG, "【音频播放】步骤2：直接播放音频数据（跳过SD卡）...");

    // 【DMA内存修复】检查内部RAM可用量
    size_t internal_free = heap_caps_get_free_size(MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
    ESP_LOGI(TAG, "【音频播放】内部RAM可用: %zu 字节", internal_free);

    if (internal_free < 4096) {
        ESP_LOGW(TAG, "【音频播放】内部RAM严重不足，尝试强制垃圾回收...");
        // 强制垃圾回收，释放碎片内存
        vTaskDelay(pdMS_TO_TICKS(10));
        internal_free = heap_caps_get_free_size(MALLOC_CAP_INTERNAL | MALLOC_CAP_8BIT);
        ESP_LOGI(TAG, "【音频播放】垃圾回收后内部RAM: %zu 字节", internal_free);
    }

    // 【DMA内存修复】直接播放音频数据，避免SD卡DMA内存需求
    ESP_LOGI(TAG, "【音频播放】步骤2成功：准备直接播放音频数据，大小:%u字节", (unsigned)audio_size);

    ESP_LOGI(TAG, "【音频播放】步骤3：直接播放音频数据...");

    // 【DMA内存修复】直接播放音频数据，避免SD卡操作
    ESP_LOGI(TAG, "【音频播放】开始直接播放音频，大小:%u字节", (unsigned)audio_size);

    // 获取播报器实例中的TTS播放器
    time_announcer_t *announcer_instance = (time_announcer_t *)announcer;
    tts_audio_player_handle_t tts_player = (tts_audio_player_handle_t)announcer_instance->tts_audio_player;

    if (tts_player == NULL) {
        ESP_LOGE(TAG, "【音频播放】TTS专用播放器未初始化");
        return TTS_ERR_AUDIO_PLAY;
    }

    // 构建音频数据结构
    tts_audio_data_t audio_data_struct = {
        .data = (uint8_t*)audio_data,
        .size = audio_size,
        .sample_rate = 16000,  // 百度TTS默认采样率
        .channels = 1,         // 单声道
        .bits_per_sample = 16  // 16位
    };

    // 调用TTS音频播放器直接播放音频数据
    esp_err_t play_result = tts_audio_player_play_data(tts_player, &audio_data_struct);

    if (play_result != ESP_OK) {
        ESP_LOGE(TAG, "【音频播放】步骤3失败：直接播放音频失败，错误码:%d", play_result);
        return TTS_ERR_AUDIO_PLAY;
    }

    ESP_LOGI(TAG, "【音频播放】步骤3成功：音频播放启动成功");

    ESP_LOGI(TAG, "【音频播放】步骤4：等待播放完成...");

    // 【用户优化】根据音频大小估算超时时间，提升响应性
    uint32_t timeout_ms = ((audio_size / 1000) + 2) * 1000;
    if (timeout_ms < 5000) timeout_ms = 5000;   // 最少5秒
    if (timeout_ms > 8000) timeout_ms = 8000;   // 【用户优化】最多8秒，提升响应性

    ESP_LOGI(TAG, "【音频播放】音频大小:%u字节，超时时间:%u毫秒", (unsigned)audio_size, (unsigned)timeout_ms);

    esp_err_t wait_result = tts_audio_player_wait_done(tts_player, timeout_ms);
    if (wait_result == ESP_OK) {
        ESP_LOGI(TAG, "【音频播放】步骤4成功：音频播放完成");
    } else if (wait_result == ESP_ERR_TIMEOUT) {
        ESP_LOGW(TAG, "【音频播放】步骤4警告：播放超时");
    } else {
        ESP_LOGE(TAG, "【音频播放】步骤4错误：播放失败: %s", esp_err_to_name(wait_result));
    }

    ESP_LOGI(TAG, "【音频播放】全部完成：TTS音频播放成功！");
    return TTS_OK;
}

tts_error_t time_announcer_update_config(time_announcer_handle_t announcer,
                                         const tts_config_t *config) {
    if (announcer == NULL || config == NULL) {
        return TTS_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "【配置更新】开始更新配置 - 整点播报: %s",
             config->hourly_announce_enabled ? "启用" : "禁用");

    // 检查整点播报开关状态变化
    bool hourly_changed = (announcer->config.hourly_announce_enabled != config->hourly_announce_enabled);

    // 更新配置
    memcpy(&announcer->config, config, sizeof(tts_config_t));

    // 更新TTS客户端配置
    tts_error_t err = baidu_tts_update_config(announcer->tts_client, config);
    if (err != TTS_OK) {
        return err;
    }

    // 【关键修复】根据新配置管理整点播报定时器状态
    if (hourly_changed) {
        ESP_LOGI(TAG, "【配置更新】整点播报开关状态发生变化，更新定时器状态");

        if (config->hourly_announce_enabled) {
            // 启用精确整点播报
            ESP_LOGI(TAG, "【配置更新】启用精确整点播报定时器");
            time_announcer_setup_precise_hourly_timer(announcer);
            ESP_LOGI(TAG, "【配置更新】精确整点播报定时器设置成功");
        } else {
            // 禁用整点播报
            if (announcer->hourly_timer != NULL) {
                BaseType_t timer_ret = xTimerStop(announcer->hourly_timer, 0);
                if (timer_ret == pdPASS) {
                    ESP_LOGI(TAG, "【配置更新】整点播报定时器停止成功");
                } else {
                    ESP_LOGW(TAG, "【配置更新】整点播报定时器停止失败: %d", timer_ret);
                }
            }
        }
    }

    // 保存配置到NVS
    time_announcer_save_config_to_nvs(config);

    ESP_LOGI(TAG, "【配置更新】配置更新完成 - 整点播报定时器状态: %s",
             config->hourly_announce_enabled ? "运行中" : "已停止");
    return TTS_OK;
}

time_announcer_state_t time_announcer_get_state(time_announcer_handle_t announcer) {
    return (announcer != NULL) ? announcer->state : TIME_ANNOUNCER_STATE_ERROR;
}

const char* time_announcer_get_error_message(time_announcer_handle_t announcer) {
    return (announcer != NULL) ? announcer->error_message : "Invalid announcer";
}

tts_error_t time_announcer_save_config_to_nvs(const tts_config_t *config) {
    if (config == NULL) {
        return TTS_ERR_INVALID_PARAM;
    }

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open(TTS_NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS namespace: %s", esp_err_to_name(err));
        return TTS_ERR_CACHE;
    }

    // 保存各项配置
    nvs_set_u8(nvs_handle, TTS_NVS_KEY_ENABLED, config->time_announce_enabled ? 1 : 0);
    nvs_set_u8(nvs_handle, TTS_NVS_KEY_HOURLY, config->hourly_announce_enabled ? 1 : 0);
    nvs_set_u8(nvs_handle, TTS_NVS_KEY_WEATHER, config->weather_announce_enabled ? 1 : 0);
    nvs_set_u8(nvs_handle, TTS_NVS_KEY_DRIVING_TIPS, config->driving_tips_enabled ? 1 : 0);
    nvs_set_u8(nvs_handle, TTS_NVS_KEY_START_HOUR, config->announce_start_hour);
    nvs_set_u8(nvs_handle, TTS_NVS_KEY_END_HOUR, config->announce_end_hour);
    nvs_set_u8(nvs_handle, TTS_NVS_KEY_VOICE_TYPE, config->voice_type);
    nvs_set_u8(nvs_handle, TTS_NVS_KEY_VOICE_SPEED, config->voice_speed);
    nvs_set_u8(nvs_handle, TTS_NVS_KEY_VOICE_PITCH, config->voice_pitch);
    nvs_set_u8(nvs_handle, TTS_NVS_KEY_VOICE_VOLUME, config->voice_volume);

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);

    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to save config to NVS: %s", esp_err_to_name(err));
        return TTS_ERR_CACHE;
    }

    ESP_LOGI(TAG, "TTS configuration saved to NVS");
    return TTS_OK;
}

tts_error_t time_announcer_load_config_from_nvs(tts_config_t *config) {
    if (config == NULL) {
        return TTS_ERR_INVALID_PARAM;
    }

    // 设置默认值
    *config = (tts_config_t)TTS_DEFAULT_CONFIG();

    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open(TTS_NVS_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGW(TAG, "Failed to open NVS namespace, using defaults: %s", esp_err_to_name(err));
        return TTS_OK; // 使用默认配置
    }

    // 读取各项配置
    uint8_t value;

    if (nvs_get_u8(nvs_handle, TTS_NVS_KEY_ENABLED, &value) == ESP_OK) {
        config->time_announce_enabled = (value != 0);
    }
    if (nvs_get_u8(nvs_handle, TTS_NVS_KEY_HOURLY, &value) == ESP_OK) {
        config->hourly_announce_enabled = (value != 0);
    }
    if (nvs_get_u8(nvs_handle, TTS_NVS_KEY_WEATHER, &value) == ESP_OK) {
        config->weather_announce_enabled = (value != 0);
    }
    if (nvs_get_u8(nvs_handle, TTS_NVS_KEY_DRIVING_TIPS, &value) == ESP_OK) {
        config->driving_tips_enabled = (value != 0);
    }
    if (nvs_get_u8(nvs_handle, TTS_NVS_KEY_START_HOUR, &value) == ESP_OK) {
        config->announce_start_hour = value;
    }
    if (nvs_get_u8(nvs_handle, TTS_NVS_KEY_END_HOUR, &value) == ESP_OK) {
        config->announce_end_hour = value;
    }
    if (nvs_get_u8(nvs_handle, TTS_NVS_KEY_VOICE_TYPE, &value) == ESP_OK) {
        config->voice_type = value;
    }
    if (nvs_get_u8(nvs_handle, TTS_NVS_KEY_VOICE_SPEED, &value) == ESP_OK) {
        config->voice_speed = value;
    }
    if (nvs_get_u8(nvs_handle, TTS_NVS_KEY_VOICE_PITCH, &value) == ESP_OK) {
        config->voice_pitch = value;
    }
    if (nvs_get_u8(nvs_handle, TTS_NVS_KEY_VOICE_VOLUME, &value) == ESP_OK) {
        config->voice_volume = value;
    }

    nvs_close(nvs_handle);

    ESP_LOGI(TAG, "TTS configuration loaded from NVS");
    return TTS_OK;
}

tts_error_t time_announcer_test_tts(time_announcer_handle_t announcer) {
    ESP_LOGI(TAG, "");
    ESP_LOGI(TAG, "🚀 ===== TTS功能测试开始 =====");
    ESP_LOGI(TAG, "");

    // 执行分步骤诊断
    tts_error_t diagnosis_result = time_announcer_step_by_step_diagnosis(announcer);
    if (diagnosis_result != TTS_OK) {
        ESP_LOGE(TAG, "❌ TTS诊断失败，错误码: %d", diagnosis_result);
        return diagnosis_result;
    }

    ESP_LOGI(TAG, "📋 步骤8: 等待任务处理");
    ESP_LOGI(TAG, "   8.1 等待2秒，观察任务是否处理请求...");

    // 等待任务处理
    vTaskDelay(pdMS_TO_TICKS(2000));

    ESP_LOGI(TAG, "   8.2 检查队列状态...");
    UBaseType_t queue_waiting_after = uxQueueMessagesWaiting(announcer->request_queue);
    ESP_LOGI(TAG, "   8.2 处理后队列等待消息: %d", queue_waiting_after);

    if (queue_waiting_after == 0) {
        ESP_LOGI(TAG, "   ✅ 8.2 请求已被处理");
    } else {
        ESP_LOGW(TAG, "   ⚠️  8.2 请求可能未被处理");
    }

    ESP_LOGI(TAG, "🎯 ===== TTS功能测试完成 =====");
    ESP_LOGI(TAG, "📊 测试结果: %s", (queue_waiting_after == 0) ? "成功" : "部分成功");
    ESP_LOGI(TAG, "");

    return TTS_OK;

    // 以下是原来的测试代码，暂时保留
    if (announcer == NULL) {
        ESP_LOGE(TAG, "【TTS测试】步骤1失败：播报器句柄为空");
        return TTS_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "【TTS测试】步骤1：开始TTS功能测试...");
    ESP_LOGI(TAG, "【TTS测试】步骤2：检查播报器状态 - 运行状态:%d, 当前状态:%d",
             announcer->is_running, announcer->state);

    // 详细检查任务状态
    ESP_LOGI(TAG, "【TTS测试】任务句柄：%p", announcer->task_handle);
    ESP_LOGI(TAG, "【TTS测试】队列句柄：%p", announcer->request_queue);

    if (announcer->task_handle != NULL) {
        ESP_LOGI(TAG, "【TTS测试】检查任务状态...");

        // 安全检查任务状态
        eTaskState task_state = eTaskGetState(announcer->task_handle);
        ESP_LOGI(TAG, "【TTS测试】任务状态：%d (0=运行,1=就绪,2=阻塞,3=暂停,4=删除)", task_state);

        // 只有在任务有效时才检查栈状态
        if (task_state != eDeleted && task_state != eInvalid) {
            ESP_LOGI(TAG, "【TTS测试】任务有效，检查栈状态...");

            // 使用安全的方式检查栈
            __asm__ __volatile__("" ::: "memory");  // 内存屏障

            UBaseType_t stack_high_water = uxTaskGetStackHighWaterMark(announcer->task_handle);
            ESP_LOGI(TAG, "【TTS测试】任务栈剩余：%d字节", stack_high_water * sizeof(StackType_t));
        } else {
            ESP_LOGE(TAG, "【TTS测试】任务已删除或无效，状态：%d", task_state);
            // 重置任务句柄
            announcer->task_handle = NULL;
            announcer->is_running = false;
        }
    } else {
        ESP_LOGE(TAG, "【TTS测试】任务句柄为空！");
    }

    if (!announcer->is_running) {
        ESP_LOGE(TAG, "【TTS测试】步骤2失败：播报器未运行");
        ESP_LOGI(TAG, "【TTS测试】尝试启动播报器...");

        // 尝试启动播报器
        tts_error_t start_err = time_announcer_start(announcer);
        if (start_err != TTS_OK) {
            ESP_LOGE(TAG, "【TTS测试】播报器启动失败，错误码:%d", start_err);
            return start_err;
        }

        ESP_LOGI(TAG, "【TTS测试】播报器启动成功，重新检查状态...");
        ESP_LOGI(TAG, "【TTS测试】新状态 - 运行状态:%d, 任务句柄:%p",
                 announcer->is_running, announcer->task_handle);

        if (!announcer->is_running || announcer->task_handle == NULL) {
            ESP_LOGE(TAG, "【TTS测试】播报器启动后仍然无效");
            return TTS_ERR_INVALID_STATE;
        }
    }

    ESP_LOGI(TAG, "【TTS测试】步骤3：获取当前时间...");

    // 获取当前时间
    time_t now;
    time(&now);
    struct tm time_info;
    localtime_r(&now, &time_info);

    ESP_LOGI(TAG, "【TTS测试】步骤3成功：当前时间 %04d-%02d-%02d %02d:%02d:%02d",
             time_info.tm_year + 1900, time_info.tm_mon + 1, time_info.tm_mday,
             time_info.tm_hour, time_info.tm_min, time_info.tm_sec);

    ESP_LOGI(TAG, "【TTS测试】步骤4：发送强制时间播报请求（测试模式）...");

    // 强制播报当前时间
    tts_error_t result = time_announcer_request(announcer, TTS_REQUEST_TYPE_TIME, TTS_PRIORITY_HIGH, true);

    if (result == TTS_OK) {
        ESP_LOGI(TAG, "【TTS测试】步骤4成功：测试请求已发送到队列");

        // 等待一小段时间，然后检查任务状态
        vTaskDelay(pdMS_TO_TICKS(500));
        ESP_LOGI(TAG, "【TTS测试】步骤5：检查任务处理状态...");
        time_announcer_check_task_status(announcer);
    } else {
        ESP_LOGE(TAG, "【TTS测试】步骤4失败：发送测试请求失败，错误码:%d", result);
    }

    return result;
}

tts_error_t time_announcer_check_task_status(time_announcer_handle_t announcer) {
    if (announcer == NULL) {
        ESP_LOGE(TAG, "【任务检查】播报器句柄为空");
        return TTS_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "【任务检查】=== 播报器状态检查 ===");
    ESP_LOGI(TAG, "【任务检查】播报器地址：%p", announcer);
    ESP_LOGI(TAG, "【任务检查】运行状态：%d", announcer->is_running);
    ESP_LOGI(TAG, "【任务检查】当前状态：%d", announcer->state);
    ESP_LOGI(TAG, "【任务检查】任务句柄：%p", announcer->task_handle);
    ESP_LOGI(TAG, "【任务检查】队列句柄：%p", announcer->request_queue);

    if (announcer->task_handle != NULL) {
        ESP_LOGI(TAG, "【任务检查】检查任务状态...");

        // 安全检查任务状态
        eTaskState task_state = eTaskGetState(announcer->task_handle);
        const char* state_names[] = {"运行", "就绪", "阻塞", "暂停", "删除", "无效"};
        const char* state_name = (task_state <= eInvalid) ? state_names[task_state] : "未知";
        ESP_LOGI(TAG, "【任务检查】任务状态：%d (%s)", task_state, state_name);

        // 只有在任务有效时才进行进一步检查
        if (task_state != eDeleted && task_state != eInvalid) {
            ESP_LOGI(TAG, "【任务检查】任务有效，获取详细信息...");

            // 安全获取任务名称
            const char* task_name = pcTaskGetName(announcer->task_handle);
            ESP_LOGI(TAG, "【任务检查】任务名称：%s", task_name ? task_name : "未知");

            // 安全检查栈状态
            __asm__ __volatile__("" ::: "memory");  // 内存屏障
            UBaseType_t stack_high_water = uxTaskGetStackHighWaterMark(announcer->task_handle);
            ESP_LOGI(TAG, "【任务检查】栈剩余：%d字节", stack_high_water * sizeof(StackType_t));
        } else {
            ESP_LOGE(TAG, "【任务检查】任务已删除或无效！");
            // 清理无效的任务句柄
            announcer->task_handle = NULL;
            announcer->is_running = false;
            ESP_LOGI(TAG, "【任务检查】已重置任务状态");
        }
    } else {
        ESP_LOGE(TAG, "【任务检查】任务句柄为空！");
    }

    if (announcer->request_queue != NULL) {
        UBaseType_t queue_spaces = uxQueueSpacesAvailable(announcer->request_queue);
        UBaseType_t queue_waiting = uxQueueMessagesWaiting(announcer->request_queue);
        ESP_LOGI(TAG, "【任务检查】队列状态 - 可用空间:%d, 等待消息:%d", queue_spaces, queue_waiting);
    } else {
        ESP_LOGE(TAG, "【任务检查】队列句柄为空！");
    }

    ESP_LOGI(TAG, "【任务检查】=== 检查完成 ===");
    return TTS_OK;
}

tts_error_t time_announcer_step_by_step_diagnosis(time_announcer_handle_t announcer) {
    ESP_LOGI(TAG, "");
    ESP_LOGI(TAG, "🔍 ===== TTS分步骤诊断开始 =====");
    ESP_LOGI(TAG, "");

    // ========== 步骤1: 基础参数检查 ==========
    ESP_LOGI(TAG, "📋 步骤1: 基础参数检查");
    ESP_LOGI(TAG, "   1.1 检查播报器句柄...");

    if (announcer == NULL) {
        ESP_LOGE(TAG, "   ❌ 1.1 失败: 播报器句柄为空");
        return TTS_ERR_INVALID_PARAM;
    }
    ESP_LOGI(TAG, "   ✅ 1.1 成功: 播报器句柄有效 (%p)", announcer);

    ESP_LOGI(TAG, "   1.2 检查TTS客户端...");
    if (announcer->tts_client == NULL) {
        ESP_LOGE(TAG, "   ❌ 1.2 失败: TTS客户端为空");
        return TTS_ERR_INVALID_PARAM;
    }
    ESP_LOGI(TAG, "   ✅ 1.2 成功: TTS客户端有效 (%p)", announcer->tts_client);

    ESP_LOGI(TAG, "   1.3 检查请求队列...");
    if (announcer->request_queue == NULL) {
        ESP_LOGE(TAG, "   ❌ 1.3 失败: 请求队列为空");
        return TTS_ERR_INVALID_PARAM;
    }
    ESP_LOGI(TAG, "   ✅ 1.3 成功: 请求队列有效 (%p)", announcer->request_queue);

    ESP_LOGI(TAG, "📋 步骤1: 基础参数检查 - 全部通过 ✅");
    ESP_LOGI(TAG, "");

    // ========== 步骤2: 系统状态检查 ==========
    ESP_LOGI(TAG, "📋 步骤2: 系统状态检查");
    ESP_LOGI(TAG, "   2.1 播报器运行状态: %s", announcer->is_running ? "运行中" : "未运行");
    ESP_LOGI(TAG, "   2.2 播报器当前状态: %d", announcer->state);
    ESP_LOGI(TAG, "   2.3 任务句柄: %p", announcer->task_handle);

    // 检查内存状态
    size_t free_heap = esp_get_free_heap_size();
    size_t min_free_heap = esp_get_minimum_free_heap_size();
    ESP_LOGI(TAG, "   2.4 内存状态: 可用=%u字节, 历史最小=%u字节", (unsigned)free_heap, (unsigned)min_free_heap);

    if (free_heap < 20480) {  // 少于20KB
        ESP_LOGW(TAG, "   ⚠️  2.4 警告: 可用内存较少");
    } else {
        ESP_LOGI(TAG, "   ✅ 2.4 成功: 内存充足");
    }

    ESP_LOGI(TAG, "📋 步骤2: 系统状态检查 - 完成");
    ESP_LOGI(TAG, "");

    // ========== 步骤3: 任务状态诊断 ==========
    ESP_LOGI(TAG, "📋 步骤3: 任务状态诊断");

    if (announcer->task_handle == NULL) {
        ESP_LOGE(TAG, "   ❌ 3.1 失败: 任务句柄为空，任务未创建");
        ESP_LOGI(TAG, "   🔧 3.1 尝试启动任务...");

        tts_error_t start_err = time_announcer_start(announcer);
        if (start_err != TTS_OK) {
            ESP_LOGE(TAG, "   ❌ 3.1 任务启动失败: %d", start_err);
            return start_err;
        }
        ESP_LOGI(TAG, "   ✅ 3.1 任务启动成功");
    } else {
        ESP_LOGI(TAG, "   3.1 任务句柄存在，检查任务状态...");

        eTaskState task_state = eTaskGetState(announcer->task_handle);
        const char* state_names[] = {"运行", "就绪", "阻塞", "暂停", "删除", "无效"};
        const char* state_name = (task_state <= eInvalid) ? state_names[task_state] : "未知";
        ESP_LOGI(TAG, "   3.2 任务状态: %d (%s)", task_state, state_name);

        if (task_state == eDeleted || task_state == eInvalid) {
            ESP_LOGE(TAG, "   ❌ 3.2 任务已删除或无效，重新创建...");
            announcer->task_handle = NULL;
            announcer->is_running = false;

            tts_error_t start_err = time_announcer_start(announcer);
            if (start_err != TTS_OK) {
                ESP_LOGE(TAG, "   ❌ 3.2 任务重新创建失败: %d", start_err);
                return start_err;
            }
            ESP_LOGI(TAG, "   ✅ 3.2 任务重新创建成功");
        } else {
            ESP_LOGI(TAG, "   ✅ 3.2 任务状态正常");
        }
    }

    ESP_LOGI(TAG, "📋 步骤3: 任务状态诊断 - 完成");
    ESP_LOGI(TAG, "");

    // ========== 步骤4: 队列状态检查 ==========
    ESP_LOGI(TAG, "📋 步骤4: 队列状态检查");

    UBaseType_t queue_spaces = uxQueueSpacesAvailable(announcer->request_queue);
    UBaseType_t queue_waiting = uxQueueMessagesWaiting(announcer->request_queue);
    ESP_LOGI(TAG, "   4.1 队列状态: 可用空间=%d, 等待消息=%d", queue_spaces, queue_waiting);

    if (queue_spaces == 0) {
        ESP_LOGW(TAG, "   ⚠️  4.1 警告: 队列已满");
    } else {
        ESP_LOGI(TAG, "   ✅ 4.1 队列状态正常");
    }

    ESP_LOGI(TAG, "📋 步骤4: 队列状态检查 - 完成");
    ESP_LOGI(TAG, "");

    // ========== 步骤5: 网络连接检查 ==========
    ESP_LOGI(TAG, "📋 步骤5: 网络连接检查");

    ESP_LOGI(TAG, "   5.1 检查WiFi连接状态...");

    // 检查内存状态（网络连接需要足够内存）
    size_t free_heap_net = esp_get_free_heap_size();
    ESP_LOGI(TAG, "   5.2 网络操作前内存状态: %u字节", (unsigned)free_heap_net);

    if (free_heap_net < 50000) {  // 少于50KB可能影响网络连接
        ESP_LOGW(TAG, "   ⚠️  5.2 警告: 可用内存较少，可能影响网络连接");
    } else {
        ESP_LOGI(TAG, "   ✅ 5.2 内存充足，支持网络连接");
    }

    // 5.3 实际网络连通性测试
    ESP_LOGI(TAG, "   5.3 测试百度服务器连通性...");
    tts_error_t net_test_err = baidu_tts_test_network_connectivity(announcer->tts_client);
    if (net_test_err != TTS_OK) {
        ESP_LOGE(TAG, "   ❌ 5.3 失败: 网络连通性测试失败，错误码:%d", net_test_err);
        return net_test_err;
    }
    ESP_LOGI(TAG, "   ✅ 5.3 成功: 百度服务器连通性正常");

    ESP_LOGI(TAG, "📋 步骤5: 网络连接检查 - 完成");
    ESP_LOGI(TAG, "");

    // ========== 步骤6: 文本生成测试 ==========
    ESP_LOGI(TAG, "📋 步骤6: 文本生成测试");

    ESP_LOGI(TAG, "   6.1 获取当前时间...");
    time_t now;
    time(&now);
    struct tm time_info;
    localtime_r(&now, &time_info);
    ESP_LOGI(TAG, "   ✅ 6.1 当前时间: %04d-%02d-%02d %02d:%02d:%02d",
             time_info.tm_year + 1900, time_info.tm_mon + 1, time_info.tm_mday,
             time_info.tm_hour, time_info.tm_min, time_info.tm_sec);

    ESP_LOGI(TAG, "   6.2 生成时间文本...");
    char time_text[TTS_MAX_TEXT_LENGTH];
    tts_error_t text_err = time_announcer_generate_time_text(&time_info, TTS_REQUEST_TYPE_TIME,
                                                             time_text, sizeof(time_text));
    if (text_err != TTS_OK) {
        ESP_LOGE(TAG, "   ❌ 6.2 文本生成失败: %d", text_err);
        return text_err;
    }
    ESP_LOGI(TAG, "   ✅ 6.2 生成的文本: \"%s\"", time_text);

    ESP_LOGI(TAG, "📋 步骤6: 文本生成测试 - 完成");
    ESP_LOGI(TAG, "");

    // ========== 步骤7: 请求发送测试 ==========
    ESP_LOGI(TAG, "📋 步骤7: 请求发送测试");

    ESP_LOGI(TAG, "   7.1 创建测试请求...");
    time_announce_request_t request = {
        .type = TTS_REQUEST_TYPE_TIME,
        .priority = TTS_PRIORITY_HIGH,
        .force_announce = true,
        .time_info = time_info
    };
    ESP_LOGI(TAG, "   ✅ 7.1 测试请求创建成功");

    ESP_LOGI(TAG, "   7.2 发送请求到队列...");
    BaseType_t queue_result = xQueueSend(announcer->request_queue, &request, pdMS_TO_TICKS(1000));
    if (queue_result != pdPASS) {
        ESP_LOGE(TAG, "   ❌ 7.2 队列发送失败: %d", queue_result);
        return TTS_ERR_TIMEOUT;
    }
    ESP_LOGI(TAG, "   ✅ 7.2 请求发送成功");

    ESP_LOGI(TAG, "📋 步骤7: 请求发送测试 - 完成");
    ESP_LOGI(TAG, "");

    ESP_LOGI(TAG, "🎯 ===== TTS分步骤诊断完成 =====");
    ESP_LOGI(TAG, "📊 诊断结果: 所有基础检查通过，请求已发送");
    ESP_LOGI(TAG, "⏳ 等待任务处理请求...");
    ESP_LOGI(TAG, "");

    return TTS_OK;
}

bool time_announcer_is_playing(time_announcer_handle_t announcer) {
    if (announcer == NULL) {
        return false;
    }

    // 检查播报器是否正在播放状态
    if (announcer->state == TIME_ANNOUNCER_STATE_PLAYING) {
        return true;
    }

    // 检查TTS专用播放器是否正在播放
    if (announcer->tts_audio_player != NULL) {
        tts_audio_player_handle_t tts_player = (tts_audio_player_handle_t)announcer->tts_audio_player;
        return tts_audio_player_is_playing(tts_player);
    }

    return false;
}

/********************************************************************
 * 文本播报功能
 ********************************************************************/

tts_error_t time_announcer_speak_text(time_announcer_handle_t announcer, const char *text) {
    if (announcer == NULL || text == NULL) {
        return TTS_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "开始文本播报: %s", text);

    // 【TTS冲突修复】检查播报器状态，如果忙碌则等待
    if (announcer->state != TIME_ANNOUNCER_STATE_IDLE) {
        ESP_LOGW(TAG, "播报器忙碌中，等待3秒后重试...");

        // 等待最多3秒
        for (int i = 0; i < 30; i++) {
            vTaskDelay(pdMS_TO_TICKS(100));  // 等待100ms
            if (announcer->state == TIME_ANNOUNCER_STATE_IDLE) {
                ESP_LOGI(TAG, "播报器已空闲，继续播报");
                break;
            }
        }

        // 再次检查状态
        if (announcer->state != TIME_ANNOUNCER_STATE_IDLE) {
            ESP_LOGW(TAG, "播报器仍然忙碌，跳过本次播报");
            return TTS_ERR_INVALID_STATE;
        }
    }

    // 设置状态为生成中
    announcer->state = TIME_ANNOUNCER_STATE_GENERATING;

    // 使用百度TTS生成语音
    baidu_tts_client_handle_t tts_client = announcer->tts_client;
    if (tts_client == NULL) {
        ESP_LOGE(TAG, "TTS客户端未初始化");
        announcer->state = TIME_ANNOUNCER_STATE_ERROR;
        return TTS_ERR_INVALID_PARAM;
    }

    // 生成TTS音频
    uint8_t *audio_data = NULL;
    size_t audio_size = 0;

    tts_error_t tts_err = baidu_tts_synthesize(tts_client, text, &audio_data, &audio_size);
    if (tts_err != TTS_OK) {
        ESP_LOGE(TAG, "TTS合成失败: %d", tts_err);
        announcer->state = TIME_ANNOUNCER_STATE_ERROR;
        return TTS_ERR_NETWORK;
    }

    ESP_LOGI(TAG, "TTS合成成功，音频大小: %zu bytes", audio_size);

    // 播放音频
    announcer->state = TIME_ANNOUNCER_STATE_PLAYING;

    if (announcer->tts_audio_player != NULL) {
        tts_audio_player_handle_t tts_player = (tts_audio_player_handle_t)announcer->tts_audio_player;

        // 构建音频数据结构
        tts_audio_data_t audio_data_struct = {
            .data = audio_data,
            .size = audio_size,
            .sample_rate = 16000,  // 百度TTS默认采样率
            .channels = 1,         // 单声道
            .bits_per_sample = 16  // 16位
        };

        esp_err_t play_err = tts_audio_player_play_data(tts_player, &audio_data_struct);

        if (play_err != ESP_OK) {
            ESP_LOGE(TAG, "音频播放失败: %s", esp_err_to_name(play_err));
            free(audio_data);
            announcer->state = TIME_ANNOUNCER_STATE_ERROR;
            return TTS_ERR_AUDIO;
        }

        ESP_LOGI(TAG, "文本播报开始播放");
    } else {
        ESP_LOGW(TAG, "音频播放器未初始化，跳过播放");
        free(audio_data);
        announcer->state = TIME_ANNOUNCER_STATE_IDLE;
        return TTS_ERR_INVALID_PARAM;
    }

    // 释放音频数据
    free(audio_data);

    // 等待播放完成（简单实现，实际应该使用回调）
    vTaskDelay(pdMS_TO_TICKS(100));  // 短暂延迟
    announcer->state = TIME_ANNOUNCER_STATE_IDLE;

    ESP_LOGI(TAG, "文本播报完成");
    return TTS_OK;
}

/**
 * @brief 检查精确定时器状态
 * 【新增】监控定时器运行状态，确保整点播报正常工作
 */
static void time_announcer_check_timer_status(time_announcer_handle_t announcer) {
    if (announcer == NULL) {
        ESP_LOGE(TAG, "【定时器监控】播报器句柄为空");
        return;
    }

    if (!announcer->config.hourly_announce_enabled) {
        ESP_LOGD(TAG, "【定时器监控】整点播报未启用，跳过检查");
        return;
    }

    if (announcer->hourly_timer == NULL) {
        ESP_LOGW(TAG, "【定时器监控】精确定时器为空，尝试重新创建");
        time_announcer_setup_precise_hourly_timer(announcer);
        return;
    }

    // 检查定时器是否正在运行
    BaseType_t timer_active = xTimerIsTimerActive(announcer->hourly_timer);
    if (timer_active == pdFALSE) {
        ESP_LOGW(TAG, "【定时器监控】精确定时器未运行，尝试重新启动");
        time_announcer_setup_precise_hourly_timer(announcer);
    } else {
        // 计算剩余时间
        TickType_t remaining_ticks = xTimerGetExpiryTime(announcer->hourly_timer) - xTaskGetTickCount();
        uint32_t remaining_seconds = remaining_ticks / configTICK_RATE_HZ;

        ESP_LOGI(TAG, "【定时器监控】精确定时器正常运行，剩余 %lu 秒触发",
                 (unsigned long)remaining_seconds);
    }
}
